import Image from "next/image";
import Link from "next/link";
import { FaFacebookF, FaInstagram } from "react-icons/fa";
import { getDictionary } from "../lib/get-translation";

const Footer = async ({ lang = "fr" }: { lang?: "en" | "fr" }) => {
  const dictionary = await getDictionary(lang);
  const { footer } = dictionary;

  const weartLinks = {
    home: lang === 'fr' ? '/fr' : '/',
    about: lang === 'fr' ? '/fr/a-propos' : '/about',
    artists: lang === 'fr' ? '/fr/artistes' : '/artists',
    primary: lang === 'fr' ? '/fr/marche-primaire' : '/primary-market',
    secondary: lang === 'fr' ? '/fr/marche-secondaire' : '/secondary-market',
    gallery: lang === 'fr' ? '/fr/galerie' : '/gallery',
  };

  return (
    <footer className="bg-black text-white pt-20 pb-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-center mb-16">
          <Image
            src="/logo-powered-by-weex.svg"
            alt="WEART Powered by WEEX"
            width={160}
            height={40}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-6 gap-8">
          <div className="md:col-span-2 flex justify-center md:justify-start mb-8 md:mb-0">
            <Image
              src="/logo-footer.svg"
              alt="WEART"
              width={140}
              height={140}
            />
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-5 col-span-1 md:col-span-4 gap-8 text-sm">
            <div>
              <h3 className="font-bold mb-4">{footer.weart.title}</h3>
              <ul>
                <li className="mb-2">
                  <Link href={weartLinks.home} className="hover:underline">
                    {footer.weart.home}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href={weartLinks.about} className="hover:underline">
                    {footer.weart.about}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href={weartLinks.artists} className="hover:underline">
                    {footer.weart.artists}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href={weartLinks.primary} className="hover:underline">
                    {footer.weart.primary}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href={weartLinks.secondary} className="hover:underline">
                    {footer.weart.secondary}
                  </Link>
                </li>
                <li>
                  <Link href={weartLinks.gallery} className="hover:underline">
                    {footer.weart.gallery}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">{footer.divers.title}</h3>
              <ul>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.divers.team}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.divers.press}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.divers.faq}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.divers.blog}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:underline">
                    {footer.divers.opening}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">{footer.weex.title}</h3>
              <ul>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.weex.board}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.weex.structure}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.weex.depot}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.weex.security}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:underline">
                    {footer.weex.coverage}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">{footer.contact.title}</h3>
              <ul>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.contact.write}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.contact.meet}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.contact.apply}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:underline">
                    {footer.contact.collaborate}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">{footer.legal.title}</h3>
              <ul>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.legal.terms}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.legal.conditions}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.legal.privacy}
                  </Link>
                </li>
                <li className="mb-2">
                  <Link href="#" className="hover:underline">
                    {footer.legal.disclosure}
                  </Link>
                </li>
                <li>
                  <Link href="#" className="hover:underline">
                    {footer.legal.cookies}
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center text-xs text-gray-400">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <a
                href="#"
                aria-label="Instagram"
                className="border border-white p-1.5"
              >
                <FaInstagram className="h-5 w-5" />
              </a>
              <a
                href="#"
                aria-label="Facebook"
                className="border border-white p-1.5"
              >
                <FaFacebookF className="h-5 w-5" />
              </a>
            </div>
            <div className="text-center mb-4 md:mb-0">
              <p>{footer.copyright}</p>
              <p>{footer.license}</p>
            </div>
            <div className="flex items-center space-x-4">
              <Image
                src="/logo-blockchain.svg"
                alt="Blockchain"
                width={100}
                height={24}
              />
              <Image
                src="/logo-kaleido.png"
                alt="Kaleido"
                width={100}
                height={24}
              />
              <Image
                src="/logo-lab-ft.svg"
                alt="Lab FT"
                width={100}
                height={24}
              />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
