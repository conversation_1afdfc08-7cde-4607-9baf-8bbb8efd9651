"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from 'next/image';
import { Dictionary } from "../../lib/get-translation";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { z } from 'zod'; // 1. Import zod

const karla = Karla({ subsets: ["latin"], weight: ["400", "700"] });
const prata = Prata({ subsets: ["latin"], weight: ["400"] });

interface RegisterPageProps {
  dictionary: Dictionary;
  lang: string;
}

interface FormData {
  first_name: string;
  last_name: string;
  slug: string;
  country_code: string;
  number: string;
  email: string;
  password: string;
  country: string;
  acceptTermsAndPrivacy: boolean;
}

interface ApiError {
  error: {
    en: string;
    fr: string;
    code: number;
  };
}

// 2. Define a validation schema that uses your dictionary for messages
const getRegisterSchema = (dictionary: Dictionary) => z.object({
  first_name: z.string().min(1, dictionary.auth.register.validation.name_required),
  last_name: z.string().min(1, dictionary.auth.register.validation.last_name_required),
  email: z.string().email(dictionary.auth.register.validation.invalid_email),
  number: z.string().min(1, dictionary.auth.register.validation.phone_required),
  password: z.string().min(8, dictionary.auth.register.validation.password_length),
  acceptTermsAndPrivacy: z.boolean().refine(val => val === true, {
    message: dictionary.auth.register.validation.accept_terms
  })
});


export default function RegisterPage({ dictionary, lang }: RegisterPageProps) {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  // 3. Rename 'error' to 'apiError' and add 'formErrors' state
  const [apiError, setApiError] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isMounted, setIsMounted] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    first_name: "",
    last_name: "",
    slug: "tn",
    country_code: "216",
    number: "",
    email: "",
    password: "",
    country: "TN",
    acceptTermsAndPrivacy: false,
  });

  const registerSchema = getRegisterSchema(dictionary);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 4. Update handleSubmit to use the validation schema
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault(); // This is important to prevent default browser behavior
    setIsLoading(true);
    setApiError("");
    setFormErrors({});

    const result = registerSchema.safeParse(formData);

    if (!result.success) {
      const errors: Record<string, string> = {};
      result.error.issues.forEach(issue => {
        errors[issue.path[0]] = issue.message;
      });
      setFormErrors(errors);
      setIsLoading(false);
      return;
    }

    try {
      const payload = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        slug: formData.slug,
        country_code: formData.country_code,
        number: formData.number,
        email: formData.email,
        password: formData.password,
        curr: "USD",
        lang: lang,
        device_token: "string",
        device_type: "web",
        country: formData.country,
        quick_sign_up: false
      };

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        const apiError = data as ApiError;
        const errorMessage = apiError.error[lang as 'en' | 'fr'] || dictionary.auth.register.error;
        throw new Error(errorMessage);
      }

      const loginPath = lang === "fr" ? "/fr/connexion" : "/login";
      router.push(loginPath);
    } catch (err) {
      setApiError(err instanceof Error ? err.message : dictionary.auth.register.error);
    } finally {
      setIsLoading(false);
    }
  };

  // 5. Update handleInputChange to clear errors on change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    const newValue = type === "checkbox" ? (e.target as HTMLInputElement).checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue,
    }));

    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  if (!isMounted) {
    return null;
  }

  return (
    <div
      className={`min-h-screen flex flex-col justify-center ${karla.className}`}
      style={{
        backgroundColor: '#ffffff',
        color: '#171717',
        '--background': '#ffffff',
        '--foreground': '#171717'
      } as React.CSSProperties}
    >
      <div className="flex flex-col lg:flex-row mt-8 lg:mt-16 mb-8 lg:mb-15">
        <div className="w-full lg:w-1/2 flex flex-col justify-center p-8 lg:pl-56">
          <div className="w-full max-w-md lg:-mr-16">
            <h1 className={`text-4xl text-gray-900 ${prata.className} mb-15`}>
              {dictionary.auth.register.title}
            </h1>
            {/* 6. Add noValidate to form and remove 'required' from inputs below */}
            <form onSubmit={handleSubmit} className="space-y-9" noValidate>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {dictionary.auth.register.first_name} <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="first_name"
                    className="w-full mt-1 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                    value={formData.first_name}
                    onChange={handleInputChange}
                  />
                  {formErrors.first_name && <p className="text-red-500 text-xs mt-1">{formErrors.first_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {dictionary.auth.register.last_name} <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="last_name"
                    className="w-full mt-1 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                    value={formData.last_name}
                    onChange={handleInputChange}
                  />
                  {formErrors.last_name && <p className="text-red-500 text-xs mt-1">{formErrors.last_name}</p>}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {dictionary.auth.register.phone} <span className="text-red-500">*</span>
                </label>
                <div className="flex mt-1">
                  <div className="flex items-center py-2 pr-3 bg-white border-b border-gray-300">
                    <span className="w-6 h-4 bg-red-600 rounded-sm mr-2"></span>
                    <span className="text-sm">+{formData.country_code}</span>
                  </div>
                  <input
                    type="tel"
                    name="number"
                    className="flex-1 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                    value={formData.number}
                    onChange={handleInputChange}
                  />
                </div>
                {formErrors.number && <p className="text-red-500 text-xs mt-1">{formErrors.number}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {dictionary.auth.register.country} <span className="text-red-500">*</span>
                </label>
                <select
                  name="country"
                  className="w-full mt-1 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm appearance-none"
                  value={formData.country}
                  onChange={handleInputChange}
                >
                  <option value="TN">{dictionary.auth.register.tunisia}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {dictionary.auth.register.email} <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  className="w-full mt-1 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                  value={formData.email}
                  onChange={handleInputChange}
                />
                {formErrors.email && <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  {dictionary.auth.register.password} <span className="text-red-500">*</span>
                </label>
                <div className="relative mt-1">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    className="w-full py-2 pr-10 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <AiOutlineEyeInvisible className="h-5 w-5 text-black" />
                    ) : (
                      <AiOutlineEye className="h-5 w-5 text-black" />
                    )}
                  </button>
                </div>
                {formErrors.password && <p className="text-red-500 text-xs mt-1">{formErrors.password}</p>}
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-black text-white py-3 px-4 font-medium hover:bg-[var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 ease-in-out"
              >
                {isLoading
                  ? dictionary.auth.register.submitting
                  : dictionary.auth.register.submit.toUpperCase()
                }
              </button>
              <div className="space-y-3 text-sm pt-4">
                <p className="text-black">
                  {lang === "fr"
                    ? "En cochant cette case, je reconnais avoir pris connaissance et accepté :"
                    : "By checking this box, I acknowledge having read and accepted:"
                  }
                </p>

                <div className="flex items-start space-x-2">
                  <input
                    type="checkbox"
                    name="acceptTermsAndPrivacy"
                    id="acceptTermsAndPrivacy"
                    className="mt-0.5 h-4 w-4 text-black focus:ring-black border-gray-300 rounded-none"
                    checked={formData.acceptTermsAndPrivacy}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="acceptTermsAndPrivacy" className="text-gray-700">
                    {lang === "fr" ? "J'accepte " : "I accept "}
                    <a href="#" className="text-[var(--color-primary)] hover:underline">
                      {dictionary.auth.register.terms}
                      <span className="text-[var(--color-primary)]">{lang === "fr" ? " et la " : " and the "}</span>
                      {dictionary.auth.register.privacy}.
                    </a>
                  </label>
                </div>
                {formErrors.acceptTermsAndPrivacy && <p className="text-red-500 text-xs mt-1">{formErrors.acceptTermsAndPrivacy}</p>}
              </div>

              {apiError && (
                <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
                  {apiError}
                </div>
              )}
            </form>
          </div>
        </div>

        <div className="hidden lg:flex lg:w-1/2 items-center justify-center">
          <Image
            src="/dollar.png"
            alt={dictionary.auth.register.title || 'Registration page artwork'}
            width={622}
            height={588}
            className="object-contain lg:-ml-99 lg:mt-9"
          />
        </div>
      </div>
    </div>
  );
}