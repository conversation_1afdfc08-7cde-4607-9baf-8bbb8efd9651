"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Dictionary } from "../../lib/get-translation";
import { <PERSON><PERSON>, P<PERSON> } from "next/font/google";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { z } from 'zod'; // Import zod

const karla = Karla({ subsets: ["latin"], weight: ["400", "700"] });
const prata = Prata({ subsets: ["latin"], weight: ["400"] });

interface LoginPageProps {
  dictionary: Dictionary;
  lang: string;
}

// Define validation schema using dictionary for messages
const getLoginSchema = (dictionary: Dictionary) => z.object({
  email: z.string().email(dictionary.auth.login.validation?.invalid_email || "Invalid email format"),
  password: z.string().min(8, dictionary.auth.login.validation?.password_length || "Password must be at least 8 characters")
});

export default function LoginPage({ dictionary, lang }: LoginPageProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const loginSchema = getLoginSchema(dictionary);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setApiError("");
    setFormErrors({});

    const result = loginSchema.safeParse(formData);

    if (!result.success) {
      const errors: Record<string, string> = {};
      result.error.issues.forEach(issue => {
        errors[issue.path[0]] = issue.message;
      });
      setFormErrors(errors);
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          device_token: "web-app",
          device_type: "web",
        }),
      });

      if (!response.ok) {
        throw new Error(dictionary.auth.login.invalid_credentials);
      }

      const data = await response.json();
      
      localStorage.setItem("auth_token", data.token);
      localStorage.setItem("user_data", JSON.stringify(data.user));

      const dashboardPath = lang === "fr" ? "/fr/tableau-de-bord" : "/dashboard";
      router.push(dashboardPath);
    } catch (err) {
      setApiError(err instanceof Error ? err.message : dictionary.auth.login.error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear errors on change
    if (formErrors[e.target.name]) {
      setFormErrors({
        ...formErrors,
        [e.target.name]: "",
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white text-black p-4">
      {/* Title Section */}
      <div className="text-center w-full max-w-6xl flex justify-start pl-8 ">
        <h2 className={`text-4xl mb-12 ${prata.className}`}>
          {dictionary.auth.login.title}
        </h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl w-full">
        {/* Form Section */}
        <div className="p-8 ">
          <form onSubmit={handleSubmit} className="space-y-9" noValidate>
            <div>
              <label htmlFor="email" className={`block text-sm text-black ${karla.className}`}>
                {dictionary.auth.login.email} <span className="text-red-500">*</span>
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                className="mt-1 block w-full px-3 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm"
                value={formData.email}
                onChange={handleInputChange}
              />
              {formErrors.email && <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>}
            </div>
            <div>
              <label htmlFor="password" className={`block text-sm text-black ${karla.className}`}>
                {dictionary.auth.login.password} <span className="text-red-500">*</span>
              </label>
              <div className="relative mt-1">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className="block w-full px-3 py-2 bg-white border-b border-gray-300 focus:outline-none focus:ring-black focus:border-black sm:text-sm pr-10"
                  value={formData.password}
                  onChange={handleInputChange}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                >
                     {showPassword ? (
                                        <AiOutlineEyeInvisible className="h-5 w-5 text-black" />
                                      ) : (
                                        <AiOutlineEye className="h-5 w-5 text-black" />
                                      )}
                </button>
              </div>
              {formErrors.password && <p className="text-red-500 text-xs mt-1">{formErrors.password}</p>}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input id="remember-me" name="remember-me" type="checkbox" className="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 accent-yellow-600" />
                <label htmlFor="remember-me" className={`ml-2 block text-sm font-bold text-gray-900 ${karla.className}`}>
                  {dictionary.auth.login.remember_me}
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className={`font-medium text-yellow-600 hover:text-yellow-500 ${karla.className}`}>
                  {dictionary.auth.login.forgot_password}
                </a>
              </div>
            </div>

            {apiError && (
              <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">{apiError}</div>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full flex justify-center py-3 px-4 border border-transparent shadow-sm text-sm font-medium text-white bg-black hover:bg-[var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 transition-colors duration-300 ease-in-out ${karla.className}`}
              >
                {isLoading ? dictionary.auth.login.submitting : dictionary.auth.login.submit}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className={`px-6 bg-white text-black ${karla.className}`}>{dictionary.auth.login.or}</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
               <div>
                <a href="#" className="w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 shadow-sm bg-white text-sm font-medium hover:bg-gray-50 hover:border-black transition-colors duration-300 ease-in-out">
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 48 48" className="mr-2">
                    <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.574l6.19,5.238C42.021,35.596,44,30.138,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path>
                  </svg>
                  <span className={`${karla.className}`}>{dictionary.auth.login.continue_with_google}</span>
                </a>
              </div>
              <div>
                <a href="#" className="w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 shadow-sm bg-white text-sm font-medium hover:bg-gray-50 hover:border-black transition-colors duration-300 ease-in-out">
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 48 48" className="mr-2">
                    <path fill="#ff5722" d="M6 6H22V22H6z" transform="rotate(-180 14 14)"></path><path fill="#4caf50" d="M26 6H42V22H26z" transform="rotate(-180 34 14)"></path><path fill="#ffc107" d="M26 26H42V42H26z" transform="rotate(-180 34 34)"></path><path fill="#03a9f4" d="M6 26H22V42H6z" transform="rotate(-180 14 34)"></path>
                  </svg>
                  <span className={`${karla.className}`}>{dictionary.auth.login.continue_with_microsoft}</span>
                </a>
              </div>
            </div>
          </div>

          <div className="mt-8 text-center text-sm">
            <p>
              {dictionary.auth.login.no_account}{' '}
              <a href="/register" className={`font-medium text-yellow-600 underline underline-offset-4 hover:text-yellow-500 ${karla.className}`}>
                {dictionary.auth.login.sign_up}
              </a>
            </p>
          </div>
        </div>

        {/* Image Section */}
        <div className="hidden md:flex items-center justify-center p-8">
          <Image
            src="/dollar.png" 
            alt={dictionary.auth.login.image_alt}
            width={500}
            height={600}
            className="object-contain"
          />
        </div>
      </div>
    </div>
  );
}