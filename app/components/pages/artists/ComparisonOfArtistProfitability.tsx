"use client";

import { Artist, IndexArtist, Price } from "../../../types/artist";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { FiInfo } from "react-icons/fi";

interface ComparisonOfArtistProfitabilityProps {
  artists: Artist[];
  dictionary: {
    title: string;
    y_axis_label: string;
    description?: string;
  };
}

const ComparisonOfArtistProfitability: React.FC<
  ComparisonOfArtistProfitabilityProps
> = ({ artists, dictionary }) => {
  // Predefined colors for specific artists to match the design
  const artistColors: Record<string, string> = {
    "Jean-<PERSON>": "#1E88E5", // blue
    "Pierre Soul<PERSON>": "#FF9800", // orange
    "Andy Warhol": "#4CAF50", // green
    "Pablo PICASSO": "#F44336", // red
    "Zao <PERSON>-ki": "#9C27B0", // purple
    "<PERSON>": "#795548", // brown
  };

  // Fallback colors for any additional artists
  const fallbackColors = [
    "#8884d8",
    "#82ca9d",
    "#ffc658",
    "#ff7300",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
  ];

  type ChartData = {
    year: string;
    [key: string]: number | string;
  };

  const data = artists.reduce((acc: ChartData[], artist) => {
    artist.index_artist.forEach((metric: IndexArtist) => {
      let yearData = acc.find((d) => d.year === metric.year);
      if (!yearData) {
        yearData = { year: metric.year };
        acc.push(yearData);
      }
      yearData[artist.name] =
        metric.price.find((p: Price) => p.curr === "USD")?.value || 0;
    });
    return acc;
  }, []);

  data.sort((a, b) => parseInt(a.year) - parseInt(b.year));

  const getColorForArtist = (artistName: string, index: number) => {
    return (
      artistColors[artistName] || fallbackColors[index % fallbackColors.length]
    );
  };

  const description = dictionary.description || "";

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-2">
        <h2 className="text-2xl font-semibold text-gray-900">
          {dictionary.title}
        </h2>
        <FiInfo className="text-gray-400 h-5 w-5" />
      </div>

      <p className="text-sm text-gray-600 mb-6">{description}</p>

      <div className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="year"
              tick={{ fontSize: 12, fill: "#666" }}
              axisLine={{ stroke: "#e0e0e0" }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: "#666" }}
              axisLine={{ stroke: "#e0e0e0" }}
              label={{
                value: dictionary.y_axis_label,
                angle: -90,
                position: "insideLeft",
                style: { textAnchor: "middle", fill: "#666", fontSize: 12 },
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "white",
                border: "1px solid #e0e0e0",
                borderRadius: "4px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                fontSize: "12px",
              }}
            />
            <Legend
              verticalAlign="bottom"
              height={36}
              iconType="circle"
              wrapperStyle={{ fontSize: "12px", paddingTop: "10px" }}
            />
            {artists.map((artist, index) => (
              <Line
                key={artist._id}
                type="monotone"
                dataKey={artist.name}
                stroke={getColorForArtist(artist.name, index)}
                strokeWidth={2}
                dot={{ r: 3 }}
                activeDot={{ r: 5 }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ComparisonOfArtistProfitability;
