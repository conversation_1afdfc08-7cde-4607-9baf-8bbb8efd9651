"use client";

import { Artist } from "../../../types/artist";
import Image from "next/image";
import React from "react";
import {
  <PERSON>Chart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
} from "recharts";
// Import removed as no longer needed
import { FiInfo } from "react-icons/fi";

interface ArtistsShareRatingProps {
  artists: Artist[];
  dictionary: {
    title: string;
    cpa: string;
    paintings: string;
    description?: string;
    details?: string;
  };
}

const ArtistsShareRating: React.FC<ArtistsShareRatingProps> = ({
  artists,
  dictionary,
}) => {
  // Artist colors to match the design
  const artistColors: Record<string, string> = {
    "Jean-Michel Basquiat": "#9C27B0", // purple
    "Pierre Soulages": "#1E88E5", // blue
    "Andy Warhol": "#4CAF50", // green
    "Pablo PICASSO": "#FF9800", // orange
    "Zao Wou-ki": "#795548", // brown
    "David Ho<PERSON>": "#F44336", // red
  };

  const description = dictionary.description || 
    "La Cote Part (CP) des Artistes correspond à la valeur dans le temps d'une part des œuvres d'un même artiste, avec pour étalon de référence le centimètre carré";

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: string) => {
    if (!value) return "";
    const numValue = parseFloat(value);
    return `${numValue > 0 ? '+' : ''}${numValue}%`;
  };

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-2">
        <h2 className="text-2xl font-semibold text-gray-900">{dictionary.title}</h2>
        <FiInfo className="text-gray-400 h-5 w-5" />
      </div>
      
      <p className="text-sm text-gray-600 mb-6">
        {description}
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {artists.map((artist) => {
          const cpaValue = artist.ic_artist_usd;
          const percentChange = artist.ic_percentage || "0";
          const isPositive = artist.ic_direction === "up";
          const artistColor = artistColors[artist.name] || "#333333";
          
          const chartData = artist.index_artist
            .map((item) => ({
              year: item.year,
              price: item.price.find((p) => p.curr === "USD")?.value || 0,
            }))
            .sort((a, b) => parseInt(a.year) - parseInt(b.year));

          // Calculate min and max for Y axis to create proper chart scaling
          const prices = chartData.map(item => item.price);
          const minPrice = Math.min(...prices) * 0.8;
          const maxPrice = Math.max(...prices) * 1.2;
          
          // Format birth-death years
          const birthYear = artist.life_dates?.date_of_birth ? 
            new Date(artist.life_dates.date_of_birth).getFullYear() : "";
          const deathYear = artist.life_dates?.date_of_death ? 
            new Date(artist.life_dates.date_of_death).getFullYear() : "";
          const yearRange = birthYear ? 
            `(${birthYear}${deathYear ? ` - ${deathYear}` : " - "})` : "";

          return (
            <div key={artist._id} className="relative pb-8">
              <div className="flex items-center mb-3">
                <div 
                  className="w-12 h-12 rounded-full mr-3 flex items-center justify-center overflow-hidden" 
                  style={{ backgroundColor: `${artistColor}20` }}
                >
                  {artist.image ? (
                    <Image
                      src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${artist.image}`}
                      alt={artist.name}
                      width={48}
                      height={48}
                      className="rounded-full"
                    />
                  ) : (
                    <span 
                      className="text-xl font-bold" 
                      style={{ color: artistColor }}
                    >
                      {artist.name.charAt(0)}
                    </span>
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3 
                      className="text-base font-medium" 
                      style={{ color: artistColor }}
                    >
                      {artist.name}
                    </h3>
                    <span className="text-xs text-gray-500">{yearRange}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">CP Artiste</span>
                    <span 
                      className={`text-xs font-medium ${isPositive ? 'text-green-500' : 'text-red-500'}`}
                    >
                      {formatPercentage(typeof percentChange === 'string' ? percentChange : String(percentChange))}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-900">
                  {formatCurrency(typeof cpaValue === 'string' ? parseFloat(cpaValue) || 0 : cpaValue || 0)} / part
                </span>
              </div>
              
              <div className="h-32 w-full mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                    <CartesianGrid vertical={false} stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="year" 
                      tick={{ fontSize: 10, fill: '#999' }}
                      axisLine={{ stroke: '#e0e0e0' }}
                      tickLine={false}
                      interval={Math.ceil(chartData.length / 5)}
                    />
                    <YAxis 
                      domain={[minPrice, maxPrice]}
                      hide={false}
                      tick={{ fontSize: 10, fill: '#999' }}
                      axisLine={false}
                      tickLine={false}
                      tickCount={3}
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'white', 
                        border: '1px solid #e0e0e0',
                        borderRadius: '4px',
                        fontSize: '12px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                      formatter={(value) => [formatCurrency(Number(value)), '']}
                    />
                    <Line
                      type="monotone"
                      dataKey="price"
                      stroke={artistColor}
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
              
              <div className="grid grid-cols-4 gap-1">
                {artist.comp_tables.slice(0, 4).map((painting, index) => (
                  <div key={index} className="aspect-square relative overflow-hidden">
                    <Image
                      src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${painting.img}`}
                      alt={painting.painting_en || `Artwork ${index + 1}`}
                      fill
                      sizes="(max-width: 768px) 25vw, 20vw"
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
              
              <div className="mt-4">
                <button className="w-full py-2 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  {dictionary.details || "DÉTAILS"}
                </button>
              </div>
              
              {/* Divider lines between artists */}
              {(artists.indexOf(artist) + 1) % 3 !== 0 && artists.indexOf(artist) !== artists.length - 1 && (
                <div className="absolute right-0 top-0 bottom-0 w-px bg-gray-200 -mr-4"></div>
              )}
              {(artists.indexOf(artist) < artists.length - 3) && (
                <div className="absolute left-0 right-0 bottom-0 h-px bg-gray-200 -mb-4"></div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ArtistsShareRating;
