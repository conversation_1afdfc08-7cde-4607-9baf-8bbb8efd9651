"use client";

import { Artist } from "../../../types/artist";
import Image from "next/image";
import React from "react";
import { FiInfo } from "react-icons/fi";

interface ArtistSalesPerformanceProps {
  artists: Artist[];
  dictionary: {
    title: string;
    turnover: string;
    lots_sold: string;
    record_price: string;
    description?: string;
  };
}

const ArtistSalesPerformance: React.FC<ArtistSalesPerformanceProps> = ({
  artists,
  dictionary,
}) => {
  const getLatestMetric = (artist: Artist) => {
    if (!artist.metrics || artist.metrics.length === 0) return null;
    return artist.metrics.reduce((latest, metric) => {
      return parseInt(metric.year) > parseInt(latest.year) ? metric : latest;
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(value);
  };
  
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  const description = dictionary.description || 
    "Cette rubrique correspond au record de vente, ainsi qu'au chiffre d'affaires et au nombre d'œuvres vendues par artiste sur la dernière année";

  // Artist colors to match the design
  const artistColors: Record<string, string> = {
    "Jean-Michel Basquiat": "#1E88E5", // blue
    "Pierre Soulages": "#FF9800", // orange
    "Andy Warhol": "#4CAF50", // green
    "Pablo PICASSO": "#F44336", // red
    "Zao Wou-ki": "#9C27B0", // purple
    "David Hockney": "#795548", // brown
  };

  return (
    <div className="w-full">
      <div className="flex items-center gap-2 mb-2">
        <h2 className="text-2xl font-semibold text-gray-900">{dictionary.title}</h2>
        <FiInfo className="text-gray-400 h-5 w-5" />
      </div>
      
      <p className="text-sm text-gray-600 mb-6">
        {description}
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {artists.map((artist) => {
          const latestMetric = getLatestMetric(artist);
          const artistColor = artistColors[artist.name] || "#333333";
          const year = latestMetric ? latestMetric.year : "2023";
          
          return (
            <div key={artist._id} className="relative">
              <div className="flex items-center mb-5">
                <div 
                  className="w-12 h-12 rounded-full mr-3 flex items-center justify-center overflow-hidden" 
                  style={{ backgroundColor: `${artistColor}20` }}
                >
                  {artist.image ? (
                    <Image
                      src={`${process.env.NEXT_PUBLIC_BACKEND_BASE_URL}${artist.image}`}
                      alt={artist.name}
                      width={48}
                      height={48}
                      className="rounded-full"
                    />
                  ) : (
                    <span 
                      className="text-xl font-bold" 
                      style={{ color: artistColor }}
                    >
                      {artist.name.charAt(0)}
                    </span>
                  )}
                </div>
                <div>
                  <h3 
                    className="text-base font-medium" 
                    style={{ color: artistColor }}
                  >
                    {artist.name}
                  </h3>
                </div>
              </div>
              
              {latestMetric && (
                <div className="space-y-4">
                  <div>
                    <p className="text-xs text-gray-500 mb-1">{`${dictionary.turnover} ${year}`}</p>
                    <p className="text-base font-semibold text-gray-900">
                      {formatCurrency(
                        latestMetric.total_auction_sales.find(
                          (p) => p.curr === "USD"
                        )?.value || 0
                      )}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-xs text-gray-500 mb-1">{`${year} ${dictionary.lots_sold}`}</p>
                    <p className="text-base font-semibold text-gray-900">
                      {formatNumber(latestMetric.lots_sold)}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-xs text-gray-500 mb-1">{dictionary.record_price}</p>
                    <p className="text-base font-semibold text-gray-900">
                      {formatCurrency(
                        latestMetric.all_time_record_price.find(
                          (p) => p.curr === "USD"
                        )?.value || 0
                      )}
                    </p>
                  </div>
                </div>
              )}
              
              {/* Divider lines between artists */}
              {(artists.indexOf(artist) + 1) % 3 !== 0 && artists.indexOf(artist) !== artists.length - 1 && (
                <div className="absolute right-0 top-0 bottom-0 w-px bg-gray-200 -mr-4"></div>
              )}
              {(artists.indexOf(artist) < artists.length - 3) && (
                <div className="absolute left-0 right-0 bottom-0 h-px bg-gray-200 -mb-4"></div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ArtistSalesPerformance;
