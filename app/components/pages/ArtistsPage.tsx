import { getDictionary } from "../../lib/get-translation";
import { ApiResponse } from "../../types/artist";

import ArtistSalesPerformance from "./artists/ArtistSalesPerformance";
import ArtistsShareRating from "./artists/ArtistsShareRating";
import ComparisonOfArtistProfitability from "./artists/ComparisonOfArtistProfitability";

async function getArtists(): Promise<ApiResponse> {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/artists/?limit=9&offset=0&currency=EURO`,
      {
        next: { revalidate: 3600 },
      }
    );
    if (!res.ok) {
      throw new Error("Failed to fetch artists");
    }
    return res.json();
  } catch (error) {
    console.error("Error fetching artists:", error);
    // Depending on how you want to handle errors, you might want to return a default/empty state
    // For now, re-throwing the error to be handled by a higher-level error boundary.
    throw error;
  }
}

const ArtistsPage = async ({ lang }: { lang: "en" | "fr" }) => {
  const dictionary = await getDictionary(lang);
  const { artists } = await getArtists();

  return (
    <div className="container mx-auto">
      <ComparisonOfArtistProfitability
        artists={artists}
        dictionary={dictionary.artists.comparison_profitability}
      />
      <ArtistSalesPerformance
        artists={artists}
        dictionary={dictionary.artists.sales_performance}
      />
      <ArtistsShareRating
        artists={artists}
        dictionary={dictionary.artists.share_rating}
      />
    </div>
  );
};

export default ArtistsPage;
