import { getDictionary } from "../../lib/get-translation";
import Image from "next/image";

interface PaintingItem {
  _id: string;
  title: {
    en: string;
    fr: string;
  };
  img: string;
  artist?: string;
  price?: number;
  shares_available?: number;
}

interface ApiResponse {
  paintings: PaintingItem[];
  base_url: string;
}

async function getPaintingsData(): Promise<ApiResponse> {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/paintings/?type=secondary&limit=9&offset=0&currency=USD`,
      { next: { revalidate: 3600 } }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch paintings data');
    }
    const data = await response.json();
    // console.log("API Response:", data);
    return data;
  } catch (error) {
    console.error("Error fetching paintings data:", error);
    return { paintings: [], base_url: "" };
  }
}

export default async function SecondaryMarketPage({ lang }: { lang: "en" | "fr" }) {
  const [dictionary, paintingsData] = await Promise.all([
    getDictionary(lang),
    getPaintingsData(),
  ]);

  // return (
  //   <div className="min-h-screen bg-white">
  //     <div className="container mx-auto py-8">
  //       <div className="text-center mb-8">
  //         <h1 className="text-4xl font-bold text-gray-900 mb-4">
  //           {dictionary.nav.secondary_market}
  //         </h1>
  //         <p className="text-gray-600 max-w-2xl mx-auto">
  //           Discover and purchase shares of artworks on our secondary market
  //         </p>
  //       </div>

  //       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  //         {paintingsData.paintings.map((painting) => (
  //           <div key={painting._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
  //             <div className="aspect-square relative">
  //               <Image
  //                 src={`${paintingsData.base_url}${painting.img}`}
  //                 alt={painting.title[lang]}
  //                 fill
  //                 className="object-cover"
  //                 sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  //               />
  //             </div>
  //             <div className="p-4">
  //               <h3 className="text-lg font-semibold text-gray-900 mb-2">
  //                 {painting.title[lang]}
  //               </h3>
  //               {painting.artist && (
  //                 <p className="text-gray-600 mb-2">{painting.artist}</p>
  //               )}
  //               {painting.price && (
  //                 <p className="text-xl font-bold text-green-600 mb-2">
  //                   ${painting.price.toLocaleString()}
  //                 </p>
  //               )}
  //               {painting.shares_available && (
  //                 <p className="text-sm text-gray-500">
  //                   {painting.shares_available} shares available
  //                 </p>
  //               )}
  //               <button className="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
  //                 View Details
  //               </button>
  //             </div>
  //           </div>
  //         ))}
  //       </div>

  //       {paintingsData.paintings.length === 0 && (
  //         <div className="text-center py-12">
  //           <p className="text-gray-500">No paintings available in the secondary market at the moment.</p>
  //         </div>
  //       )}
  //     </div>
  //   </div>
  // );
}
