"use client";

import Image from "next/image";
import Link from "next/link";
import { Dictionary } from "../lib/get-translation";
import Ticker from "./Ticker";
import { useState, useEffect, useRef } from "react";
import { FiMenu, FiX, FiUser, FiGlobe } from "react-icons/fi";
import { Prata } from "next/font/google";

const prata = Prata({ subsets: ["latin"], weight: "400" });

interface Kpi {
  _id: string;
  artist_name: string;
  value_euro: string;
  value_usd: string;
  percentage: string;
  direction: "up" | "down";
}

interface HeaderProps {
  kpis: Kpi[];
  dictionary: Dictionary;
  lang: "en" | "fr";
}

const Header = ({ kpis, dictionary, lang }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState("$");
  const [selectedLanguage, setSelectedLanguage] = useState(lang);
  const [languageDisplay, setLanguageDisplay] = useState(lang === 'en' ? dictionary.nav.english : dictionary.nav.french);
  const [currencyDisplay, setCurrencyDisplay] = useState("$");
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const navLinks = [
    { href: "/about", label: dictionary.nav.about },
    { href: "/artists", label: dictionary.nav.artists },
    { href: "/primary-market", label: dictionary.nav.primary_market },
    { href: "/secondary-market", label: dictionary.nav.secondary_market },
    { href: "/gallery", label: dictionary.nav.gallery },
  ];

  const getLocalizedPath = (path: string) => {
    if (lang === "fr") {
      const frSlugs: { [key: string]: string } = {
        "/about": "/a-propos",
        "/artists": "/artistes",
        "/primary-market": "/marche-primaire",
        "/secondary-market": "/marche-secondaire",
        "/gallery": "/galerie",
      };
      return `/fr${frSlugs[path] || path}`;
    }
    return path;
  };

  return (
    <header className="bg-white shadow-sm lg:shadow-none">
      <Ticker kpis={kpis} partLabel={dictionary.kpi.part} />

      {/* Mobile and Tablet Header */}
      <div className="lg:hidden">
        <nav className="container mx-auto px-4 py-3 flex justify-between items-center">
          <button onClick={() => setIsMenuOpen(true)} className="text-2xl">
            <FiMenu />
          </button>
          <div className="absolute left-1/2 -translate-x-1/2">
            <Link href={lang === "fr" ? "/fr" : "/"}>
              <Image src="/logo.svg" alt="WeArt Logo" width={120} height={35} className="w-auto h-8 sm:h-9" />
            </Link>
          </div>
          <div className="flex items-center space-x-3 sm:space-x-4 text-xl sm:text-2xl">
            <FiUser />
            <FiGlobe />
          </div>
        </nav>
        <div className="bg-[#B9924F] text-white text-center py-3 font-bold uppercase text-sm sm:text-base">
          <button className="w-full">{dictionary.user.buy}</button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 bg-white z-30 flex flex-col p-4 sm:p-6">
          <div className="flex justify-between items-center mb-8 sm:mb-10">
            <Link href={lang === "fr" ? "/fr" : "/"}>
              <Image src="/logo.svg" alt="WeArt Logo" width={120} height={35} className="w-auto h-8 sm:h-9" />
            </Link>
            <button onClick={() => setIsMenuOpen(false)} className="text-2xl sm:text-3xl">
              <FiX strokeWidth={1} />
            </button>
          </div>
          <nav className="flex flex-col space-y-3 sm:space-y-4 text-base sm:text-lg">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={getLocalizedPath(link.href)}
                className="py-2 border-b border-gray-200 uppercase tracking-wide font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
          </nav>
          <div className="mt-auto">
            <button className="w-full bg-[#B9924F] text-white text-center py-3 font-bold uppercase text-sm sm:text-base">
              {dictionary.user.buy}
            </button>
          </div>
        </div>
      )}

      {/* Desktop Header */}
      <div className="hidden lg:flex container mx-auto px-4 lg:px-8 xl:px-12 relative">
        <nav className="flex w-full items-center justify-between py-4 text-sm xl:text-base">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href={lang === "fr" ? "/fr" : "/"}>
              <Image src="/logo.svg" alt="WeArt Logo" width={170} height={49} className="w-auto h-10 lg:h-12" />
            </Link>
          </div>

          {/* Navigation Links */}
          <div className="hidden lg:flex items-center space-x-4 xl:space-x-6 uppercase text-sm xl:text-base tracking-wide font-medium">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={getLocalizedPath(link.href)}
                className="text-black hover:text-black whitespace-nowrap"
              >
                {link.label}
              </Link>
            ))}
          </div>
          
          {/* Auth / Actions */}
          <div className="hidden lg:flex items-center space-x-2 xl:space-x-3 font-medium uppercase text-sm xl:text-base flex-shrink-0">
            <Link
              href={lang === "fr" ? "/fr/inscription" : "/register"}
              className="text-black hover:text-black whitespace-nowrap"
            >
              {dictionary.user.register}
            </Link>
            <span className="text-[#B9924F]">|</span>
            <Link
              href={lang === "fr" ? "/fr/connexion" : "/login"}
              className="text-black hover:text-black whitespace-nowrap"
            >
              {dictionary.user.login}
            </Link>

            <div className="relative ml-2 xl:ml-4">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center text-black hover:text-black whitespace-nowrap"
              >
                <span className="text-sm xl:text-base">{`${selectedLanguage.toUpperCase()} / ${selectedCurrency}`}</span>
                <svg className="ml-1 w-3 h-3 xl:w-4 xl:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
            
            <div className="ml-2 xl:ml-4">
              <button className="flex items-center justify-center px-6 xl:px-8 py-2 xl:py-3 bg-[var(--color-primary)] text-white uppercase font-semibold hover:bg-[#a7813d] text-sm xl:text-base whitespace-nowrap">
                {dictionary.user.buy}
              </button>
            </div>
          </div>
        </nav>
        {/* Responsive border that spans from logo to buy button */}
        <div className="absolute left-4 lg:left-8 xl:left-12 right-4 lg:right-8 xl:right-12 bottom-0 border-b border-gray-200"></div>
      </div>

      {isDropdownOpen && (
        <div className="fixed inset-0 z-40 flex items-center justify-center p-4">
          <div ref={dropdownRef} className="relative bg-white p-6 sm:p-8 md:p-12 w-full max-w-sm sm:max-w-md md:max-w-3xl min-h-[400px] sm:min-h-[450px] shadow-2xl border border-gray-200 rounded-lg">
            <button
              onClick={() => setIsDropdownOpen(false)}
              className="absolute top-3 right-3 sm:top-4 sm:right-4 text-2xl sm:text-3xl text-black"
            >
              <FiX strokeWidth={1} />
            </button>

            <h2 className={`text-2xl sm:text-3xl md:text-4xl mb-8 sm:mb-12 md:mb-16 text-center md:text-left ${prata.className}`}>{dictionary.modal.title}</h2>

            <div className="space-y-6 sm:space-y-8 md:space-y-10">
              {/* Language Selector */}
              <div className="relative">
                <button onClick={() => { setIsLanguageOpen(!isLanguageOpen); setIsCurrencyOpen(false); }} className="w-full flex justify-between items-center py-3 px-4 border-b border-gray-200">
                  <span className="text-base sm:text-lg">{languageDisplay}</span>
                  <svg className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${isLanguageOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isLanguageOpen && (
                  <div className="absolute top-full left-0 w-full bg-white shadow-lg z-10 rounded-b-md">
                    <div className="divide-y divide-gray-200">
                      <button onClick={() => { setLanguageDisplay(dictionary.nav.language); setIsLanguageOpen(false); }} className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.language ? 'bg-[#B9924F] text-white' : 'hover:bg-gray-100'}`}>{dictionary.nav.language}</button>
                      <button
                        onClick={() => { setSelectedLanguage("en"); setLanguageDisplay(dictionary.nav.english); setIsLanguageOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.english ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        {dictionary.nav.english}
                      </button>
                      <button
                        onClick={() => { setSelectedLanguage("fr"); setLanguageDisplay(dictionary.nav.french); setIsLanguageOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${languageDisplay === dictionary.nav.french ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        {dictionary.nav.french}
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Currency Selector */}
              <div className="relative">
                <button onClick={() => { setIsCurrencyOpen(!isCurrencyOpen); setIsLanguageOpen(false); }} className="w-full flex justify-between items-center py-3 px-4 border-b border-gray-200">
                  <span className="text-base sm:text-lg">{currencyDisplay}</span>
                  <svg className={`w-4 h-4 sm:w-5 sm:h-5 transition-transform ${isCurrencyOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isCurrencyOpen && (
                  <div className="absolute top-full left-0 w-full bg-white shadow-lg z-10 rounded-b-md">
                    <div className="divide-y divide-gray-200">
                      <button onClick={() => { setCurrencyDisplay(dictionary.nav.currency); setIsCurrencyOpen(false); }} className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === dictionary.nav.currency ? 'bg-[var(--color-primary)] text-white' : 'hover:bg-gray-100'}`}>{dictionary.nav.currency}</button>
                      <button
                        onClick={() => { setSelectedCurrency("$"); setCurrencyDisplay("$"); setIsCurrencyOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === "$" ? "bg-[var(--color-primary)] text-white" : "hover:bg-gray-100"}`}>
                        $
                      </button>
                      <button
                        onClick={() => { setSelectedCurrency("€"); setCurrencyDisplay("€"); setIsCurrencyOpen(false); }}
                        className={`w-full text-left py-3 px-4 text-sm sm:text-base ${currencyDisplay === "€" ? "bg-[var(--color-primary)]  text-white" : "hover:bg-gray-100"}`}>
                        €
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-8 sm:mt-12 flex justify-center">
              <button
                onClick={() => setIsDropdownOpen(false)}
                className="bg-black text-white px-6 sm:px-8 py-2 sm:py-3 font-semibold tracking-wider uppercase text-sm sm:text-base"
              >
                {dictionary.modal.save_preferences}
              </button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
